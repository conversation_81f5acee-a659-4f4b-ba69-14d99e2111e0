{"editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.formatOnSave": true, "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "terminal.integrated.localEchoStyle": "dim", "search.exclude": {"**/node_modules": true}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[graphql]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never"}