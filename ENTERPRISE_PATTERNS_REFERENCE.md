# Enterprise Patterns Reference Guide

## 🎯 Overview

This repository combines enterprise-level patterns from three cutting-edge sources:
- **[Eververse](https://github.com/haydenbleasel/eververse)** - Open-source product management platform
- **[Next-Forge](https://github.com/vercel/next-forge)** - Production-grade Turborepo template
- **[Convex v1](https://convex.dev)** - Enterprise backend platform with real-time capabilities

## 📚 Architecture Patterns

### 1. Monorepo Structure (from Next-Forge)

```
reputable-reference/
├── apps/
│   ├── web/          # Marketing & landing pages
│   └── app/          # Main application
├── packages/
│   ├── ui/           # Shared UI components
│   ├── database/     # Convex schema & functions
│   ├── analytics/    # Analytics integration
│   └── email/        # Email templates
├── tooling/
│   ├── biome/        # Linting & formatting
│   ├── typescript/   # TypeScript configs
│   └── tailwind/     # Tailwind configs
└── turbo.json        # Turborepo configuration
```

### 2. Database Architecture (from Convex v1)

#### Schema Design Principles
```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // User management with auth integration
  users: defineTable({
    email: v.string(),
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    role: v.union(v.literal("admin"), v.literal("user")),
    stripeCustomerId: v.optional(v.string()),
    subscriptionStatus: v.optional(v.string()),
  })
    .index("by_email", ["email"])
    .index("by_stripe", ["stripeCustomerId"]),

  // Organizations for team features
  organizations: defineTable({
    name: v.string(),
    slug: v.string(),
    ownerId: v.id("users"),
    plan: v.string(),
    limits: v.object({
      seats: v.number(),
      projects: v.number(),
      storage: v.number(),
    }),
  })
    .index("by_slug", ["slug"])
    .index("by_owner", ["ownerId"]),

  // Feature flags for gradual rollouts
  featureFlags: defineTable({
    key: v.string(),
    enabled: v.boolean(),
    rolloutPercentage: v.optional(v.number()),
    userGroups: v.optional(v.array(v.string())),
  }).index("by_key", ["key"]),
});
```

### 3. Authentication Pattern (from Convex + Clerk)

```typescript
// convex/auth.ts
import { ConvexError } from "convex/values";
import { mutation, query } from "./_generated/server";

export const getAuthenticatedUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new ConvexError("Not authenticated");
    }
    
    // Get or create user
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();
    
    if (!user) {
      // Create new user on first login
      return await ctx.db.insert("users", {
        email: identity.email!,
        name: identity.name,
        image: identity.pictureUrl,
        role: "user",
      });
    }
    
    return user;
  },
});
```

### 4. Real-time Subscriptions (from Convex)

```typescript
// convex/subscriptions.ts
export const subscribeToOrganization = query({
  args: { organizationId: v.id("organizations") },
  handler: async (ctx, args) => {
    const user = await getAuthenticatedUser(ctx);
    
    // Check permissions
    const member = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user_and_org", (q) =>
        q.eq("userId", user._id).eq("organizationId", args.organizationId)
      )
      .first();
    
    if (!member) {
      throw new ConvexError("Not a member of this organization");
    }
    
    // Return real-time data
    return ctx.db.get(args.organizationId);
  },
});
```

### 5. API Design Patterns (from Eververse)

```typescript
// packages/api/src/patterns/crud.ts
export const createCRUDHandlers = <T>(tableName: string) => ({
  create: mutation({
    args: { data: v.any() },
    handler: async (ctx, args) => {
      const user = await getAuthenticatedUser(ctx);
      return await ctx.db.insert(tableName, {
        ...args.data,
        createdBy: user._id,
        createdAt: Date.now(),
      });
    },
  }),

  read: query({
    args: { id: v.id(tableName) },
    handler: async (ctx, args) => {
      return await ctx.db.get(args.id);
    },
  }),

  update: mutation({
    args: { id: v.id(tableName), data: v.any() },
    handler: async (ctx, args) => {
      const user = await getAuthenticatedUser(ctx);
      return await ctx.db.patch(args.id, {
        ...args.data,
        updatedBy: user._id,
        updatedAt: Date.now(),
      });
    },
  }),

  delete: mutation({
    args: { id: v.id(tableName) },
    handler: async (ctx, args) => {
      await ctx.db.delete(args.id);
    },
  }),

  list: query({
    args: {
      limit: v.optional(v.number()),
      cursor: v.optional(v.string()),
    },
    handler: async (ctx, args) => {
      const limit = args.limit ?? 10;
      return await ctx.db
        .query(tableName)
        .order("desc")
        .take(limit);
    },
  }),
});
```

## 🛠️ Development Patterns

### 1. Environment Configuration

```typescript
// packages/env/src/index.ts
import { z } from "zod";

const envSchema = z.object({
  // App
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NODE_ENV: z.enum(["development", "production", "test"]),
  
  // Convex
  NEXT_PUBLIC_CONVEX_URL: z.string().url(),
  
  // Authentication
  CLERK_SECRET_KEY: z.string(),
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: z.string(),
  
  // Payments
  STRIPE_SECRET_KEY: z.string(),
  STRIPE_WEBHOOK_SECRET: z.string(),
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string(),
  
  // Analytics
  NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),
  NEXT_PUBLIC_POSTHOG_HOST: z.string().optional(),
  
  // Monitoring
  SENTRY_DSN: z.string().optional(),
  SENTRY_AUTH_TOKEN: z.string().optional(),
});

export const env = envSchema.parse(process.env);
```

### 2. Type Safety Patterns

```typescript
// packages/types/src/index.ts
export type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };

export type AsyncResult<T, E = Error> = Promise<Result<T, E>>;

// Usage example
export async function fetchUserData(userId: string): AsyncResult<User> {
  try {
    const user = await db.get(userId);
    if (!user) {
      return { success: false, error: new Error("User not found") };
    }
    return { success: true, data: user };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}
```

### 3. Testing Patterns

```typescript
// packages/testing/src/convex-test-helpers.ts
import { ConvexTestingHelper } from "convex-test";

export const setupTestEnvironment = () => {
  const t = new ConvexTestingHelper();
  
  beforeEach(async () => {
    await t.reset();
  });
  
  afterAll(async () => {
    await t.close();
  });
  
  return t;
};

// Usage in tests
describe("User Management", () => {
  const t = setupTestEnvironment();
  
  test("creates a new user", async () => {
    const userId = await t.mutation(api.users.create, {
      email: "<EMAIL>",
      name: "Test User",
    });
    
    const user = await t.query(api.users.get, { id: userId });
    expect(user.email).toBe("<EMAIL>");
  });
});
```

## 🚀 Performance Patterns

### 1. Optimistic Updates (from Convex)

```typescript
// apps/web/hooks/use-optimistic-mutation.ts
import { useMutation } from "convex/react";
import { useState } from "react";

export function useOptimisticMutation<Args, Result>(
  mutation: any,
  options?: {
    onSuccess?: (result: Result) => void;
    onError?: (error: Error) => void;
  }
) {
  const [isPending, setIsPending] = useState(false);
  const mutate = useMutation(mutation);
  
  const execute = async (args: Args) => {
    setIsPending(true);
    try {
      const result = await mutate(args);
      options?.onSuccess?.(result);
      return result;
    } catch (error) {
      options?.onError?.(error as Error);
      throw error;
    } finally {
      setIsPending(false);
    }
  };
  
  return { execute, isPending };
}
```

### 2. Data Caching Strategy

```typescript
// packages/cache/src/index.ts
import { unstable_cache } from "next/cache";

export const cachedQuery = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyParts: string[],
  options?: {
    revalidate?: number;
    tags?: string[];
  }
) => {
  return unstable_cache(fn, keyParts, {
    revalidate: options?.revalidate ?? 60,
    tags: options?.tags,
  });
};

// Usage
export const getCachedUserData = cachedQuery(
  async (userId: string) => {
    return await convex.query(api.users.get, { id: userId });
  },
  ["user"],
  { revalidate: 300, tags: ["user-data"] }
);
```

## 🔒 Security Patterns

### 1. Input Validation

```typescript
// convex/validators.ts
import { v } from "convex/values";
import { ConvexError } from "convex/values";

export const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ConvexError("Invalid email format");
  }
  return email.toLowerCase();
};

export const sanitizeInput = (input: string) => {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
    .replace(/[<>]/g, "");
};
```

### 2. Rate Limiting

```typescript
// convex/rateLimit.ts
export const rateLimit = async (
  ctx: any,
  key: string,
  limit: number,
  windowMs: number
) => {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  const attempts = await ctx.db
    .query("rateLimits")
    .withIndex("by_key_and_time", (q) =>
      q.eq("key", key).gte("timestamp", windowStart)
    )
    .collect();
  
  if (attempts.length >= limit) {
    throw new ConvexError("Rate limit exceeded");
  }
  
  await ctx.db.insert("rateLimits", {
    key,
    timestamp: now,
  });
};
```

## 🎨 UI Component Patterns

### 1. Compound Components

```tsx
// packages/ui/src/components/DataTable/index.tsx
import { createContext, useContext } from "react";

const DataTableContext = createContext<{
  data: any[];
  columns: any[];
}>({ data: [], columns: [] });

export function DataTable({ children, data, columns }) {
  return (
    <DataTableContext.Provider value={{ data, columns }}>
      <div className="rounded-lg border">{children}</div>
    </DataTableContext.Provider>
  );
}

DataTable.Header = function Header() {
  const { columns } = useContext(DataTableContext);
  return (
    <div className="border-b bg-gray-50 px-4 py-2">
      {columns.map((col) => (
        <div key={col.key}>{col.label}</div>
      ))}
    </div>
  );
};

DataTable.Body = function Body() {
  const { data, columns } = useContext(DataTableContext);
  return (
    <div>
      {data.map((row) => (
        <div key={row.id} className="border-b px-4 py-2">
          {columns.map((col) => (
            <div key={col.key}>{row[col.key]}</div>
          ))}
        </div>
      ))}
    </div>
  );
};
```

### 2. Accessibility Patterns

```tsx
// packages/ui/src/components/Modal/index.tsx
import { useEffect, useRef } from "react";
import { createPortal } from "react-dom";

export function Modal({ isOpen, onClose, children, title }) {
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  
  useEffect(() => {
    if (isOpen) {
      closeButtonRef.current?.focus();
      
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === "Escape") onClose();
      };
      
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isOpen, onClose]);
  
  if (!isOpen) return null;
  
  return createPortal(
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    >
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <h2 id="modal-title" className="text-xl font-bold mb-4">
          {title}
        </h2>
        {children}
        <button
          ref={closeButtonRef}
          onClick={onClose}
          className="mt-4 px-4 py-2 bg-gray-200 rounded"
          aria-label="Close modal"
        >
          Close
        </button>
      </div>
    </div>,
    document.body
  );
}
```

## 📊 Monitoring & Observability

### 1. Structured Logging

```typescript
// packages/logger/src/index.ts
type LogLevel = "debug" | "info" | "warn" | "error";

interface LogContext {
  userId?: string;
  organizationId?: string;
  requestId?: string;
  [key: string]: any;
}

class Logger {
  private context: LogContext = {};
  
  setContext(context: LogContext) {
    this.context = { ...this.context, ...context };
  }
  
  private log(level: LogLevel, message: string, data?: any) {
    const entry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      ...this.context,
      ...(data && { data }),
    };
    
    if (process.env.NODE_ENV === "production") {
      // Send to monitoring service
      fetch("/api/logs", {
        method: "POST",
        body: JSON.stringify(entry),
      });
    } else {
      console.log(JSON.stringify(entry, null, 2));
    }
  }
  
  debug(message: string, data?: any) {
    this.log("debug", message, data);
  }
  
  info(message: string, data?: any) {
    this.log("info", message, data);
  }
  
  warn(message: string, data?: any) {
    this.log("warn", message, data);
  }
  
  error(message: string, error?: Error | any) {
    this.log("error", message, {
      error: error?.message,
      stack: error?.stack,
    });
  }
}

export const logger = new Logger();
```

### 2. Performance Monitoring

```typescript
// packages/monitoring/src/performance.ts
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map();
  
  mark(name: string) {
    this.marks.set(name, performance.now());
  }
  
  measure(name: string, startMark: string, endMark?: string) {
    const start = this.marks.get(startMark);
    if (!start) {
      console.warn(`Start mark "${startMark}" not found`);
      return;
    }
    
    const end = endMark ? this.marks.get(endMark) : performance.now();
    if (!end) {
      console.warn(`End mark "${endMark}" not found`);
      return;
    }
    
    const duration = end - start;
    
    // Send to analytics
    if (typeof window !== "undefined" && window.analytics) {
      window.analytics.track("Performance Metric", {
        metric: name,
        duration,
        unit: "ms",
      });
    }
    
    return duration;
  }
  
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;
      
      // Log slow operations
      if (duration > 1000) {
        logger.warn(`Slow operation: ${name}`, { duration });
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      logger.error(`Operation failed: ${name}`, {
        duration,
        error,
      });
      throw error;
    }
  }
}

export const perfMonitor = new PerformanceMonitor();
```

## 🚢 Deployment Patterns

### 1. CI/CD Configuration

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - uses: oven-sh/setup-bun@v1
      
      - name: Install dependencies
        run: bun install
      
      - name: Run tests
        run: bun test
      
      - name: Type check
        run: bun run type-check
      
      - name: Lint
        run: npx ultracite lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Vercel
        uses: vercel/action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

### 2. Feature Flags for Progressive Rollout

```typescript
// packages/feature-flags/src/index.ts
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

export function useFeatureFlag(flagKey: string): boolean {
  const flags = useQuery(api.featureFlags.getForUser);
  return flags?.[flagKey] ?? false;
}

export function FeatureFlag({
  flag,
  children,
  fallback = null,
}: {
  flag: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const isEnabled = useFeatureFlag(flag);
  return <>{isEnabled ? children : fallback}</>;
}

// Usage
<FeatureFlag flag="new-dashboard" fallback={<OldDashboard />}>
  <NewDashboard />
</FeatureFlag>
```

## 📝 Documentation Standards

### 1. API Documentation

```typescript
/**
 * Creates a new organization for the authenticated user
 * 
 * @param args.name - The organization name (3-50 characters)
 * @param args.slug - URL-safe identifier (lowercase, alphanumeric, hyphens)
 * @returns The newly created organization
 * @throws {ConvexError} If slug is already taken
 * @throws {ConvexError} If user has reached organization limit
 * 
 * @example
 * const org = await createOrganization({
 *   name: "Acme Corp",
 *   slug: "acme-corp"
 * });
 */
export const createOrganization = mutation({
  args: {
    name: v.string(),
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    // Implementation
  },
});
```

### 2. Component Documentation

```tsx
/**
 * DataTable Component
 * 
 * A flexible table component with sorting, filtering, and pagination
 * 
 * @example
 * ```tsx
 * <DataTable
 *   data={users}
 *   columns={[
 *     { key: "name", label: "Name", sortable: true },
 *     { key: "email", label: "Email" },
 *     { key: "role", label: "Role", filterable: true }
 *   ]}
 *   onRowClick={(row) => console.log(row)}
 * />
 * ```
 */
```

## 🔧 Tooling Configuration

### 1. Ultracite Configuration

```json
// biome.json
{
  "$schema": "https://biomejs.dev/schemas/2.2.0/schema.json",
  "extends": ["ultracite"],
  "files": {
    "ignore": ["node_modules", ".next", "dist", ".turbo"]
  },
  "linter": {
    "rules": {
      "correctness": {
        "noUnusedVariables": "error",
        "useExhaustiveDependencies": "warn"
      },
      "security": {
        "noHardcodedCredentials": "error"
      }
    }
  }
}
```

### 2. TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "ES2022"],
    "jsx": "preserve",
    "module": "esnext",
    "moduleResolution": "bundler",
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "incremental": true,
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules", ".next", "dist"]
}
```

## 🎯 Best Practices Summary

1. **Always use TypeScript** with strict mode enabled
2. **Implement proper error boundaries** in React components
3. **Use Convex indexes** for all queries to ensure performance
4. **Implement rate limiting** on sensitive operations
5. **Use feature flags** for gradual rollouts
6. **Write tests** for critical business logic
7. **Document APIs** with JSDoc comments
8. **Monitor performance** and errors in production
9. **Use semantic versioning** for packages
10. **Implement proper logging** with structured data

## 📚 Resources

- [Convex Documentation](https://docs.convex.dev)
- [Next.js Documentation](https://nextjs.org/docs)
- [Turborepo Documentation](https://turbo.build/repo/docs)
- [Ultracite Documentation](https://ultracite.ai)
- [Eververse Repository](https://github.com/haydenbleasel/eververse)
- [Next-Forge Repository](https://github.com/vercel/next-forge)

## 🚀 Getting Started

1. Clone this repository
2. Install dependencies: `bun install`
3. Set up environment variables: `cp .env.example .env.local`
4. Run Convex dev: `npx convex dev`
5. Start development: `bun dev`
6. Run Ultracite: `npx ultracite format`

---

This reference guide will be continuously updated as new patterns emerge and best practices evolve.

