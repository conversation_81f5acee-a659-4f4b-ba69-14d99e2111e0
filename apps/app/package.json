{"name": "@v1/app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "clean": "git clean -xdf .next .turbo node_modules", "lint": "biome lint", "format": "biome format --write .", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/core": "0.37.0", "@convex-dev/auth": "^0.0.80", "@convex-dev/polar": "npm:@erquhart/convex-polar@0.2.0-alpha.5", "@polar-sh/sdk": "^0.26.1", "@tanstack/react-form": "^0.33.0", "@tanstack/zod-form-adapter": "^0.33.0", "@v1/analytics": "workspace:*", "@v1/backend": "workspace:*", "@v1/ui": "workspace:*", "@xixixao/uploadstuff": "^0.0.5", "convex": "^1.19.2", "dub": "^0.36.5", "geist": "^1.3.1", "next": "14.2.7", "next-international": "^1.2.4", "next-safe-action": "^7.9.0", "next-themes": "^0.3.0", "nuqs": "^1.18.0", "react": "^18.3.1", "react-dom": "^18.3.1", "remeda": "^2.14.0", "zod": "^3.23.8"}, "devDependencies": {"@sentry/nextjs": "^8", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5.5.4"}}