import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@v1/ui/select';
import { cn } from '@v1/ui/utils';
import { Monitor, Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export function ThemeSwitcher({ triggerClass }: { triggerClass?: string }) {
  const { theme: currentTheme, setTheme, themes } = useTheme();
  return (
    <Select
      onValueChange={(theme) => setTheme(theme as (typeof themes)[number])}
      value={currentTheme}
    >
      <SelectTrigger
        className={cn(
          '!px-2 h-6 rounded border-primary/20 bg-secondary hover:border-primary/40',
          triggerClass
        )}
      >
        <div className="flex items-start gap-2">
          {currentTheme === 'light' ? (
            <Sun className="h-[14px] w-[14px]" />
          ) : currentTheme === 'dark' ? (
            <Moon className="h-[14px] w-[14px]" />
          ) : (
            <Monitor className="h-[14px] w-[14px]" />
          )}
          {currentTheme && (
            <span className="font-medium text-xs">
              {currentTheme.charAt(0).toUpperCase() + currentTheme.slice(1)}
            </span>
          )}
        </div>
      </SelectTrigger>
      <SelectContent>
        {themes.map((theme) => (
          <SelectItem
            className={`font-medium text-primary/60 text-sm ${theme === currentTheme && 'text-primary'}`}
            key={theme}
            value={theme}
          >
            {theme && theme.charAt(0).toUpperCase() + theme.slice(1)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

export function ThemeSwitcherHome() {
  const { setTheme, themes } = useTheme();
  return (
    <div className="flex gap-3">
      {themes.map((theme) => (
        <button
          key={theme}
          name="theme"
          onClick={() => setTheme(theme)}
          type="button"
        >
          {theme === 'light' ? (
            <Sun className="h-4 w-4 text-primary/80 hover:text-primary" />
          ) : theme === 'dark' ? (
            <Moon className="h-4 w-4 text-primary/80 hover:text-primary" />
          ) : (
            <Monitor className="h-4 w-4 text-primary/80 hover:text-primary" />
          )}
        </button>
      ))}
    </div>
  );
}
