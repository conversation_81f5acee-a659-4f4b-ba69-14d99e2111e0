'use client';

import { useAuthActions } from '@convex-dev/auth/react';
import { Button } from '@v1/ui/button';
import { Icons } from '@v1/ui/icons';

export function SignOut() {
  const { signOut } = useAuthActions();

  return (
    <Button
      className="flex items-center gap-2 font-mono"
      onClick={signOut}
      variant="outline"
    >
      <Icons.SignOut className="size-4" />
      <span>Sign out</span>
    </Button>
  );
}
