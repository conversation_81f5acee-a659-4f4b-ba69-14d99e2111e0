{"name": "@v1/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "clean": "git clean -xdf .next .turbo node_modules", "lint": "biome lint", "format": "biome format --write .", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@calcom/embed-react": "^1.5.0", "@convex-dev/auth": "^0.0.80", "@convex-dev/polar": "npm:@erquhart/convex-polar@0.2.0-alpha.3", "@v1/analytics": "workspace:*", "@v1/ui": "workspace:*", "convex": "^1.16.3", "geist": "^1.3.1", "next": "14.2.7", "react": "^18.3.1", "react-dom": "^18.3.1", "usehooks-ts": "^3.1.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5.5.4"}}