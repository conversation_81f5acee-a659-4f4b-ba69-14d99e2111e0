{"name": "v1", "private": true, "workspaces": ["packages/*", "apps/*", "tooling/*"], "scripts": {"build": "turbo build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo clean", "dev": "turbo dev --parallel", "dev:web": "turbo dev --filter=@v1/web", "dev:jobs": "turbo jobs --filter=@v1/jobs", "dev:app": "turbo dev --filter=@v1/app", "start:web": "turbo start --filter=@v1/web", "start:app": "turbo start --filter=@v1/app", "test": "turbo test --parallel", "format": "biome format --write .", "lint": "turbo lint && bun lint:repo", "lint:repo": "bunx sherif@latest", "lint:repo:fix": "bunx sherif@latest --fix", "typecheck": "turbo typecheck"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@t3-oss/env-nextjs": "^0.11.1", "chalk": "^4.1.2", "dotenv": "^16.4.5", "ora": "^5.4.1", "ts-node": "^10.9.2", "tsx": "^4.19.1", "turbo": "2.1.1", "typescript": "^5.5.4", "ultracite": "5.2.3"}, "packageManager": "bun@1.1.26", "dependencies": {"@auth/core": "0.37.0"}, "version": "0.0.1"}