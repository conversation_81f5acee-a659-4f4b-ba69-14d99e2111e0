import {
  OpenPanelComponent,
  type PostEventPayload,
  useOpenPanel,
} from '@openpanel/nextjs';
import { logger } from '@v1/logger';

const isProd = process.env.NODE_ENV === 'production';

const Provider = () => (
  <OpenPanelComponent
    clientId={process.env.NEXT_PUBLIC_OPENPANEL_CLIENT_ID!}
    trackAttributes={true}
    trackOutgoingLinks={isProd}
    trackScreenViews={isProd}
  />
);

const track = (options: { event: string } & PostEventPayload['properties']) => {
  const { track: openTrack } = useOpenPanel();

  if (!isProd) {
    logger.info('Track', options);

    return;
  }

  const { event, ...rest } = options;

  openTrack(event, rest);
};

export { Provider, track };
