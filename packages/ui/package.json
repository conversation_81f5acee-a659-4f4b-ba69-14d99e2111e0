{"name": "@v1/ui", "version": "0.1.0", "private": true, "sideEffects": false, "type": "module", "files": ["tailwind.config.ts", "postcss.config.js", "globals.css"], "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "biome check .", "format": "biome --write .", "typecheck": "tsc --noEmit"}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.4.43", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.10", "typescript": "^5.5.4"}, "exports": {"./globals.css": "./src/globals.css", "./utils": "./src/utils/index.ts", "./avatar": "./src/components/avatar.tsx", "./button": "./src/components/button.tsx", "./dropdown-menu": "./src/components/dropdown-menu.tsx", "./input": "./src/components/input.tsx", "./logo": "./src/components/logo.tsx", "./select": "./src/components/select.tsx", "./tooltip": "./src/components/tooltip.tsx", "./switch": "./src/components/switch.tsx", "./dialog": "./src/components/dialog.tsx", "./icons": "./src/components/icons.tsx", "./scroll-area": "./src/components/scroll-area.tsx", "./upload-input": "./src/components/upload-input.tsx", "./tailwind.config": "./tailwind.config.ts", "./postcss": "./postcss.config.js"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@xixixao/uploadstuff": "^0.0.5", "class-variance-authority": "^0.7.0", "lucide-react": "^0.438.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}}