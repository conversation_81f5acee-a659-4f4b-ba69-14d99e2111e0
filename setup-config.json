{"introMessage": "Let's set up your services and environment variables.", "projects": [{"id": "convex", "exportCommand": "npx convex env set {{name}} {{value}}", "importCommand": "npx convex env get {{name}}", "ignoreLogs": ["✖ Environment variable"]}, {"id": "web", "envFile": "apps/web/.env"}, {"id": "app", "envFile": "apps/app/.env"}], "steps": [{"title": "App URL", "description": "The URL of the app, used for linking from the web app.", "variables": [{"name": "NEXT_PUBLIC_APP_URL", "projects": ["web"], "details": "This is the URL of the app, used for linking from the web app.", "defaultValue": "http://localhost:3000"}], "interactive": false}, {"title": "Convex", "description": "Convex is a complete backend platform that provides a database, API, and authentication.", "instructions": "Set up a Convex project at https://dashboard.convex.dev and obtain your Convex URL.", "variables": [{"name": "NEXT_PUBLIC_CONVEX_URL", "projects": ["web", "app"], "details": "You can find your Convex DeploymentURL in the Convex dashboard under 'Settings' > 'URL & Deploy Key'. It should look like 'https://example-123.convex.cloud'.", "template": "{{convexUrl}}", "defaultValue": "https://example-123.convex.cloud"}]}, {"title": "Google Authentication", "description": "Google Authentication allows users to sign in to your app using their Google accounts.", "instructions": "Set up Google OAuth 2.0 credentials for authentication. Follow the guide at https://support.google.com/cloud/answer/6158849?hl=en to create a new project and set up OAuth 2.0 credentials.", "variables": [{"name": "AUTH_GOOGLE_ID", "projects": ["convex"], "details": "The client ID from your Google OAuth 2.0 credentials. You can find this in the Google Cloud Console under 'APIs & Services' > 'Credentials'.", "info": ["Use this as your authorized redirect URI: {{convexSiteUrl}}/api/auth/callback/google"]}, {"name": "AUTH_GOOGLE_SECRET", "projects": ["convex"], "details": "The client secret from your Google OAuth 2.0 credentials. You can find this in the Google Cloud Console under 'APIs & Services' > 'Credentials'."}]}, {"title": "Polar", "description": "Polar is a billing platform that provides subscription management and billing.", "instructions": "Create an account at https://polar.sh/, set up an organization using the `Sell with Polar` button. Finally, switch sandbox mode to to get the right env variables, or go here: https://sandbox.polar.sh/dashboard/create for billing and obtain your access token and organization ID.", "variables": [{"name": "POLAR_WEBHOOK_SECRET", "projects": ["convex"], "details": "Create a webhook in the Polar dashboard under 'Settings' > 'Webhooks'. The payload format should be 'Raw'. Events to select are product.created, product.updated, subscription.created and subscription.updated. You can provide your own secret or use the 'Generate' option. Copy the secret and paste it here.", "info": ["Use this as your webhook URL: {{convexSiteUrl}}/polar/events"]}, {"name": "POLAR_ORGANIZATION_TOKEN", "projects": ["convex"], "details": "Generate an Organization Access Token in the Polar dashboard under 'Settings' > 'Developers'. Required permissions: 'checkouts:read', 'checkouts:write', 'products:read', 'products:write', 'subscriptions:read', 'subscriptions:write', 'customers:read', 'customers:write', 'checkout_links:read', 'checkout_links:write', 'customer_portal:read', 'customer_portal:write', 'customer_sessions:write'."}], "additionalInstructions": ["The url above uses the Sandbox environment for testing purposes."]}, {"title": "OpenPanel", "description": "OpenPanel is an open-source analytics platform.", "instructions": "Create an account at https://openpanel.dev and get your client ID and secret key.", "variables": [{"name": "OPENPANEL_SECRET_KEY", "projects": ["app"], "details": "In the OpenPanel dashboard under 'Settings' > 'Projects', you have to create a new client for one of your projects to get a secret key, if you don't already have one. It starts with 'sec_'."}, {"name": "NEXT_PUBLIC_OPENPANEL_CLIENT_ID", "projects": ["web", "app"], "details": "The client ID is on the same page as the secret key, under 'Settings' > 'Projects'."}], "required": false, "requiredMessage": "Skipping this step will disable analytics in the app."}, {"title": "Sentry", "description": "Sentry is a crash reporting platform that helps you monitor and fix errors in your app.", "instructions": "Set up a project on https://sentry.io and obtain your DSN and other required information.", "variables": [{"name": "SENTRY_ORG", "projects": ["app"], "details": "Your Sentry organization slug, found under 'Setting<PERSON>' > 'Organization - General Settings'. It's also the subdomain of your Sentry URL."}, {"name": "SENTRY_AUTH_TOKEN", "projects": ["app"], "details": "Generate an auth token in the Sentry dashboard under 'Setting<PERSON>' > 'Auth Tokens'."}, {"name": "SENTRY_PROJECT", "projects": ["app"], "details": "Your Sentry project ID, found under 'Settings' > 'Projects' > [Your Project] > 'Project Details'."}, {"name": "NEXT_PUBLIC_SENTRY_DSN", "projects": ["app"], "details": "You can find your Sentry DSN in the Sentry dashboard under 'Settings' > 'Projects' > [Your Project] > 'Client Keys (DSN)'. It should look like 'https://<EMAIL>/0'."}], "required": false, "requiredMessage": "Skipping this step will disable error reporting in the app."}, {"title": "Resend", "description": "Resend is a transactional email platform used to handle subscription related emails in the app.", "instructions": "Create an account at https://resend.com and get your API key.", "variables": [{"name": "RESEND_API_KEY", "projects": ["convex"], "details": "Find your API key in the Resend dashboard under 'API Keys'. It starts with 're_'."}, {"name": "RESEND_SENDER_EMAIL_AUTH", "projects": ["convex"], "details": "This should be the email address you want to use as the sender for authentication emails. Make sure it's verified in your Resend account under 'Domains'. If not provided, you'll only be able to send emails to the address on your Resend account.", "required": false}], "required": false, "requiredMessage": "Resend is required for subscription emails, but the app can run initially without it for testing purposes."}, {"title": "Cal.com", "description": "Cal.com is a calendar booking platform that powers the /talk-to-us page.", "instructions": "Set up your Cal.com account and get a calendar link. A calendar link should be just the path for a specific event, such as \"my-username/15min\".", "variables": [{"name": "NEXT_PUBLIC_CAL_LINK", "projects": ["web"], "details": "This is your public Cal.com link. It should look like 'https://cal.com/yourusername' or your custom domain if you've set one up.", "required": false}], "required": false, "requiredMessage": "Cal.com powers the /talk-to-us page, and is only required for that page to render correctly."}, {"title": "Loops", "description": "Loops is an email marketing platform, it powers the newsletter subscription form on the site.", "instructions": "Set up an account at https://loops.so and create a form for newsletter subscription.", "variables": [{"name": "LOOPS_FORM_ID", "projects": ["convex"], "details": "The ID of the Loops form you want to use, found in the Loops dashboard when viewing your form settings.", "required": false}], "required": false, "requiredMessage": "Loops is only required for the newsletter subscription form on the site."}]}